# Veritabanı Migration Rehberi

Bu rehber, uygulamanızı localStorage'dan SQLite veritabanına geçirmeniz için hazırlanmıştır.

## Ya<PERSON><PERSON><PERSON> Değişiklikler

### 1. Yeni Veritabanı Sistemi
- **Önceki**: localStorage (tarayıcı yerel depolama)
- **Yeni**: SQLite veritabanı (better-sqlite3 ile)

### 2. Avantajlar
- ✅ **Daha güvenilir veri saklama**: Veriler artık tarayıcı dışında saklanıyor
- ✅ **Daha hızlı performans**: Veritabanı sorguları localStorage'dan daha hızlı
- ✅ **İlişkisel veri yapısı**: Ürünler ve işlemler arasında doğru ilişkiler
- ✅ **Veri bütünlüğü**: Otomatik veri doğrulama ve kısıtlamalar
- ✅ **Backup kolaylığı**: <PERSON><PERSON> (dev.db) ye<PERSON><PERSON><PERSON>
- ✅ **Gelişmiş raporlama**: Daha karmaşık sorgular ve analizler mümkün

### 3. Dosya Yapısı
```
├── telefoncu.db          # SQLite veritabanı dosyası
├── lib/
│   ├── database.ts       # Veritabanı servis katmanı
│   └── db.ts            # React hooks (güncellenmiş)
```

## Otomatik Migration

Uygulama ilk açıldığında **otomatik olarak** localStorage'daki mevcut verilerinizi veritabanına aktaracaktır.

### Migration Süreci:
1. Uygulama açıldığında veritabanı kontrol edilir
2. Eğer veritabanı boşsa, localStorage'dan veriler okunur
3. Veriler otomatik olarak veritabanına aktarılır
4. localStorage verileri korunur (güvenlik için)

### Migration Durumları:
- **Yeni kullanıcı**: Örnek verilerle başlar
- **Mevcut kullanıcı**: localStorage verileri otomatik aktarılır
- **Zaten migrate edilmiş**: Hiçbir işlem yapılmaz

## Manuel Kontrol

Eğer migration'ı manuel olarak kontrol etmek istiyorsanız:

### 1. Veritabanı Durumunu Kontrol
```bash
# Veritabanı dosyasını kontrol et
ls -la telefoncu.db
```

### 2. Veritabanını Sıfırla (İsteğe bağlı)
```bash
# Veritabanını sil ve yeniden oluştur
rm telefoncu.db
# Uygulama yeniden başlatıldığında otomatik oluşturulacak
```

### 3. LocalStorage'ı Temizle (İsteğe bağlı)
Migration tamamlandıktan sonra, tarayıcı geliştirici araçlarından localStorage'ı temizleyebilirsiniz:
1. F12 ile geliştirici araçlarını açın
2. Application/Storage sekmesine gidin
3. Local Storage > localhost:3000
4. `telefoncu_urunler` ve `telefoncu_islemler` anahtarlarını silin

## Yedekleme

### Veritabanı Yedeği
```bash
# Veritabanını yedekle
cp telefoncu.db telefoncu.db.backup
```

### Veritabanını Geri Yükle
```bash
# Yedeği geri yükle
cp telefoncu.db.backup telefoncu.db
```

## Sorun Giderme

### 1. Migration Çalışmıyor
- Tarayıcı konsolunu kontrol edin (F12)
- Hata mesajları için terminal çıktısını inceleyin

### 2. Veriler Görünmüyor
- Sayfayı yenileyin (Ctrl+F5)
- Veritabanı dosyasının var olduğunu kontrol edin: `ls -la telefoncu.db`

### 3. Performans Sorunları
- Veritabanı dosyasının boyutunu kontrol edin
- Gerekirse veritabanını optimize edin

### 4. Veri Kaybı
- LocalStorage verileri korunur, endişelenmeyin
- Gerekirse manuel migration yapabilirsiniz

## Geliştirici Notları

### Yeni Özellikler
- `UrunService` ve `IslemService` sınıfları eklendi
- Senkron SQLite işlemleri (daha hızlı)
- Otomatik ID oluşturma
- Tarih alanları doğru şekilde işleniyor
- İlişkisel sorgular mümkün
- WAL mode ile performans optimizasyonu

### API Değişiklikleri
- CRUD işlemleri artık senkron
- Hata yönetimi geliştirildi
- TypeScript tipleri güncellendi
- better-sqlite3 ile native performans

Bu migration ile uygulamanız daha güvenilir, hızlı ve ölçeklenebilir hale gelmiştir!
