"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CalendarIcon, Download, ArrowLeft, PieChart, BarChart } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import Link from "next/link"
import { useIslemler, raporOlustur, raporuIndir } from "@/lib/db"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, <PERSON>lt<PERSON>, <PERSON>, ArcElement } from "chart.js"
import { <PERSON>, <PERSON> } from "react-chartjs-2"

// Chart.js bileşenlerini kaydet
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement)

export default function RaporlarPage() {
  const { islemler } = useIslemler()
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().setDate(new Date().getDate() - 30)))
  const [endDate, setEndDate] = useState<Date | undefined>(new Date())

  // Rapor oluştur
  const rapor = raporOlustur(islemler, startDate, endDate)

  const handleRaporIndir = () => {
    raporuIndir(islemler, startDate, endDate)
    toast({
      title: "Başarılı",
      description: "Rapor başarıyla indirildi",
    })
  }

  return (
    <div className="container mx-auto p-4">
      <Toaster />
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Ana Sayfa
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Detaylı Raporlar</h1>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex items-center gap-2">
          <span>Başlangıç:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[180px] justify-start text-left font-normal">
                {startDate ? format(startDate, "dd MMMM yyyy", { locale: tr }) : "Tarih Seçin"}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar mode="single" selected={startDate} onSelect={setStartDate} locale={tr} />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <span>Bitiş:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[180px] justify-start text-left font-normal">
                {endDate ? format(endDate, "dd MMMM yyyy", { locale: tr }) : "Tarih Seçin"}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar mode="single" selected={endDate} onSelect={setEndDate} locale={tr} />
            </PopoverContent>
          </Popover>
        </div>

        <Button className="ml-auto" onClick={handleRaporIndir}>
          <Download className="mr-2 h-4 w-4" /> Raporu İndir
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam İşlem</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rapor.toplamIslem}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Maliyet</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{rapor.toplamMaliyet.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Satış</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{rapor.toplamSatis.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card className="bg-green-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">₺{rapor.toplamKar.toLocaleString()}</div>
            <div className="text-sm text-green-600">Kar Marjı: %{rapor.karMarji.toFixed(2)}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="islemler">
        <TabsList className="mb-4">
          <TabsTrigger value="islemler">Tüm İşlemler</TabsTrigger>
          <TabsTrigger value="kategori">Kategori Bazlı</TabsTrigger>
          <TabsTrigger value="gunluk">Günlük Özet</TabsTrigger>
          <TabsTrigger value="grafikler">Grafikler</TabsTrigger>
        </TabsList>

        <TabsContent value="islemler">
          <Card>
            <CardHeader>
              <CardTitle>Tüm İşlemler</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Ürün/Hizmet</TableHead>
                    <TableHead>Maliyet (₺)</TableHead>
                    <TableHead>Satış Fiyatı (₺)</TableHead>
                    <TableHead>Kar (₺)</TableHead>
                    <TableHead>Müşteri</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rapor.islemler.length > 0 ? (
                    rapor.islemler.map((islem) => (
                      <TableRow key={islem.id}>
                        <TableCell>{islem.id}</TableCell>
                        <TableCell>{format(islem.tarih, "dd.MM.yyyy")}</TableCell>
                        <TableCell>{islem.kategori}</TableCell>
                        <TableCell>{islem.urunAdi}</TableCell>
                        <TableCell>{islem.alisFiyati.toLocaleString()}</TableCell>
                        <TableCell>{islem.satisFiyati.toLocaleString()}</TableCell>
                        <TableCell className="font-medium text-green-600">{islem.kar.toLocaleString()}</TableCell>
                        <TableCell>{islem.musteriAdi}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4 text-muted-foreground">
                        Bu tarih aralığında işlem bulunamadı
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="kategori">
          <Card>
            <CardHeader>
              <CardTitle>Kategori Bazlı Özet</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kategori</TableHead>
                    <TableHead>İşlem Sayısı</TableHead>
                    <TableHead>Toplam Maliyet (₺)</TableHead>
                    <TableHead>Toplam Satış (₺)</TableHead>
                    <TableHead>Toplam Kar (₺)</TableHead>
                    <TableHead>Kar Marjı (%)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(rapor.kategoriBazli).length > 0 ? (
                    Object.entries(rapor.kategoriBazli).map(([kategori, data]) => (
                      <TableRow key={kategori}>
                        <TableCell className="font-medium">{kategori}</TableCell>
                        <TableCell>{data.islemSayisi}</TableCell>
                        <TableCell>{data.toplamMaliyet.toLocaleString()}</TableCell>
                        <TableCell>{data.toplamSatis.toLocaleString()}</TableCell>
                        <TableCell className="font-medium text-green-600">{data.toplamKar.toLocaleString()}</TableCell>
                        <TableCell>
                          {data.toplamSatis > 0 ? ((data.toplamKar / data.toplamSatis) * 100).toFixed(2) : "0.00"}%
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        Bu tarih aralığında işlem bulunamadı
                      </TableCell>
                    </TableRow>
                  )}
                  {Object.entries(rapor.kategoriBazli).length > 0 && (
                    <TableRow className="bg-gray-50 font-bold">
                      <TableCell>TOPLAM</TableCell>
                      <TableCell>{rapor.toplamIslem}</TableCell>
                      <TableCell>{rapor.toplamMaliyet.toLocaleString()}</TableCell>
                      <TableCell>{rapor.toplamSatis.toLocaleString()}</TableCell>
                      <TableCell className="text-green-600">{rapor.toplamKar.toLocaleString()}</TableCell>
                      <TableCell>{rapor.karMarji.toFixed(2)}%</TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gunluk">
          <Card>
            <CardHeader>
              <CardTitle>Günlük Özet</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tarih</TableHead>
                    <TableHead>İşlem Sayısı</TableHead>
                    <TableHead>Toplam Maliyet (₺)</TableHead>
                    <TableHead>Toplam Satış (₺)</TableHead>
                    <TableHead>Toplam Kar (₺)</TableHead>
                    <TableHead>Kar Marjı (%)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.values(rapor.gunlukOzet).length > 0 ? (
                    Object.values(rapor.gunlukOzet)
                      .sort((a, b) => b.tarih.getTime() - a.tarih.getTime())
                      .map((data, index) => (
                        <TableRow key={index}>
                          <TableCell>{format(data.tarih, "dd.MM.yyyy")}</TableCell>
                          <TableCell>{data.islemSayisi}</TableCell>
                          <TableCell>{data.toplamMaliyet.toLocaleString()}</TableCell>
                          <TableCell>{data.toplamSatis.toLocaleString()}</TableCell>
                          <TableCell className="font-medium text-green-600">
                            {data.toplamKar.toLocaleString()}
                          </TableCell>
                          <TableCell>
                            {data.toplamSatis > 0 ? ((data.toplamKar / data.toplamSatis) * 100).toFixed(2) : "0.00"}%
                          </TableCell>
                        </TableRow>
                      ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        Bu tarih aralığında işlem bulunamadı
                      </TableCell>
                    </TableRow>
                  )}
                  {Object.values(rapor.gunlukOzet).length > 0 && (
                    <TableRow className="bg-gray-50 font-bold">
                      <TableCell>TOPLAM</TableCell>
                      <TableCell>{rapor.toplamIslem}</TableCell>
                      <TableCell>{rapor.toplamMaliyet.toLocaleString()}</TableCell>
                      <TableCell>{rapor.toplamSatis.toLocaleString()}</TableCell>
                      <TableCell className="text-green-600">{rapor.toplamKar.toLocaleString()}</TableCell>
                      <TableCell>{rapor.karMarji.toFixed(2)}%</TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grafikler">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Kategori Dağılımı</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6">
                {Object.keys(rapor.kategoriBazli).length > 0 ? (
                  <Pie
                    data={{
                      labels: Object.keys(rapor.kategoriBazli),
                      datasets: [
                        {
                          label: "Satış Tutarı",
                          data: Object.values(rapor.kategoriBazli).map((k) => k.toplamSatis),
                          backgroundColor: [
                            "rgba(54, 162, 235, 0.6)",
                            "rgba(255, 99, 132, 0.6)",
                            "rgba(75, 192, 192, 0.6)",
                            "rgba(255, 206, 86, 0.6)",
                            "rgba(153, 102, 255, 0.6)",
                          ],
                          borderColor: [
                            "rgba(54, 162, 235, 1)",
                            "rgba(255, 99, 132, 1)",
                            "rgba(75, 192, 192, 1)",
                            "rgba(255, 206, 86, 1)",
                            "rgba(153, 102, 255, 1)",
                          ],
                          borderWidth: 1,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: "bottom",
                        },
                        title: {
                          display: true,
                          text: "Kategori Bazlı Satış Dağılımı",
                        },
                      },
                    }}
                  />
                ) : (
                  <div className="flex flex-col items-center">
                    <PieChart className="h-48 w-48 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mt-4">Veri bulunamadı</p>
                  </div>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Günlük Kar Grafiği</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6">
                {Object.values(rapor.gunlukOzet).length > 0 ? (
                  <Bar
                    data={{
                      labels: Object.values(rapor.gunlukOzet)
                        .sort((a, b) => a.tarih.getTime() - b.tarih.getTime())
                        .map((g) => format(g.tarih, "dd.MM.yyyy")),
                      datasets: [
                        {
                          label: "Günlük Kar",
                          data: Object.values(rapor.gunlukOzet)
                            .sort((a, b) => a.tarih.getTime() - b.tarih.getTime())
                            .map((g) => g.toplamKar),
                          backgroundColor: "rgba(75, 192, 192, 0.6)",
                          borderColor: "rgba(75, 192, 192, 1)",
                          borderWidth: 1,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: "top",
                        },
                        title: {
                          display: true,
                          text: "Günlük Kar Değişimi",
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }}
                  />
                ) : (
                  <div className="flex flex-col items-center">
                    <BarChart className="h-48 w-48 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mt-4">Veri bulunamadı</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
