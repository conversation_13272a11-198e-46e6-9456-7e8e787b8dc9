"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CalendarIcon, Download, ArrowLeft, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import Link from "next/link"

interface Transaction {
  id: number
  date: Date
  category: string
  product: string
  cost: number
  price: number
  profit: number
  customer: string
}

export default function DetayliRaporlar() {
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(2025, 4, 1)) // 1 Mayıs 2025
  const [endDate, setEndDate] = useState<Date | undefined>(new Date(2025, 4, 17)) // 17 Mayıs 2025
  const [reportType, setReportType] = useState("gunluk")

  // Örnek işlem verileri
  const transactions: Transaction[] = [
    {
      id: 1,
      date: new Date("2025-05-17"),
      category: "Telefon",
      product: "iPhone 13",
      cost: 18000,
      price: 22000,
      profit: 4000,
      customer: "Ahmet Yılmaz",
    },
    {
      id: 2,
      date: new Date("2025-05-16"),
      category: "Aksesuar",
      product: "iPhone 13 Silikon Kılıf",
      cost: 50,
      price: 150,
      profit: 100,
      customer: "Mehmet Demir",
    },
    {
      id: 3,
      date: new Date("2025-05-15"),
      category: "Telefon",
      product: "Samsung Galaxy S22",
      cost: 15000,
      price: 18500,
      profit: 3500,
      customer: "Ayşe Kaya",
    },
    {
      id: 4,
      date: new Date("2025-05-14"),
      category: "Aksesuar",
      product: "Type-C Şarj Kablosu",
      cost: 30,
      price: 90,
      profit: 60,
      customer: "Fatma Şahin",
    },
    {
      id: 5,
      date: new Date("2025-05-13"),
      category: "Tamir",
      product: "Ekran Değişimi",
      cost: 500,
      price: 1500,
      profit: 1000,
      customer: "Ali Yıldız",
    },
    {
      id: 6,
      date: new Date("2025-05-12"),
      category: "Aksesuar",
      product: "Bluetooth Kulaklık",
      cost: 150,
      price: 300,
      profit: 150,
      customer: "Zeynep Çelik",
    },
    {
      id: 7,
      date: new Date("2025-05-11"),
      category: "Telefon",
      product: "Xiaomi Redmi Note 11",
      cost: 6000,
      price: 7500,
      profit: 1500,
      customer: "Mustafa Kara",
    },
  ]

  // Kategori bazlı özet hesaplama
  const categoryData = transactions.reduce(
    (acc, transaction) => {
      if (!acc[transaction.category]) {
        acc[transaction.category] = {
          count: 0,
          totalCost: 0,
          totalPrice: 0,
          totalProfit: 0,
        }
      }

      acc[transaction.category].count += 1
      acc[transaction.category].totalCost += transaction.cost
      acc[transaction.category].totalPrice += transaction.price
      acc[transaction.category].totalProfit += transaction.profit

      return acc
    },
    {} as Record<string, { count: number; totalCost: number; totalPrice: number; totalProfit: number }>,
  )

  // Günlük özet hesaplama
  const dailyData = transactions.reduce(
    (acc, transaction) => {
      const dateStr = format(transaction.date, "yyyy-MM-dd")

      if (!acc[dateStr]) {
        acc[dateStr] = {
          date: transaction.date,
          count: 0,
          totalCost: 0,
          totalPrice: 0,
          totalProfit: 0,
        }
      }

      acc[dateStr].count += 1
      acc[dateStr].totalCost += transaction.cost
      acc[dateStr].totalPrice += transaction.price
      acc[dateStr].totalProfit += transaction.profit

      return acc
    },
    {} as Record<string, { date: Date; count: number; totalCost: number; totalPrice: number; totalProfit: number }>,
  )

  // Toplam değerler
  const totalTransactions = transactions.length
  const totalCost = transactions.reduce((sum, t) => sum + t.cost, 0)
  const totalPrice = transactions.reduce((sum, t) => sum + t.price, 0)
  const totalProfit = transactions.reduce((sum, t) => sum + t.profit, 0)
  const profitMargin = (totalProfit / totalPrice) * 100

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Ana Sayfa
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Detaylı Raporlar</h1>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex items-center gap-2">
          <span>Başlangıç:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[180px] justify-start text-left font-normal">
                {startDate ? format(startDate, "dd MMMM yyyy", { locale: tr }) : "Tarih Seçin"}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar mode="single" selected={startDate} onSelect={setStartDate} locale={tr} />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <span>Bitiş:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[180px] justify-start text-left font-normal">
                {endDate ? format(endDate, "dd MMMM yyyy", { locale: tr }) : "Tarih Seçin"}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar mode="single" selected={endDate} onSelect={setEndDate} locale={tr} />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <span>Rapor Türü:</span>
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Rapor Türü" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gunluk">Günlük Rapor</SelectItem>
              <SelectItem value="haftalik">Haftalık Rapor</SelectItem>
              <SelectItem value="aylik">Aylık Rapor</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button className="ml-auto">
          <Download className="mr-2 h-4 w-4" /> Raporu İndir
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam İşlem</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTransactions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Maliyet</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{totalCost.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Satış</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{totalPrice.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card className="bg-green-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">₺{totalProfit.toLocaleString()}</div>
            <div className="text-sm text-green-600">Kar Marjı: %{profitMargin.toFixed(2)}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="islemler">
        <TabsList className="mb-4">
          <TabsTrigger value="islemler">Tüm İşlemler</TabsTrigger>
          <TabsTrigger value="kategori">Kategori Bazlı</TabsTrigger>
          <TabsTrigger value="gunluk">Günlük Özet</TabsTrigger>
          <TabsTrigger value="grafikler">Grafikler</TabsTrigger>
        </TabsList>

        <TabsContent value="islemler">
          <Card>
            <CardHeader>
              <CardTitle>Tüm İşlemler</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Ürün/Hizmet</TableHead>
                    <TableHead>Maliyet (₺)</TableHead>
                    <TableHead>Satış Fiyatı (₺)</TableHead>
                    <TableHead>Kar (₺)</TableHead>
                    <TableHead>Müşteri</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.id}</TableCell>
                      <TableCell>{format(transaction.date, "dd.MM.yyyy")}</TableCell>
                      <TableCell>{transaction.category}</TableCell>
                      <TableCell>{transaction.product}</TableCell>
                      <TableCell>{transaction.cost.toLocaleString()}</TableCell>
                      <TableCell>{transaction.price.toLocaleString()}</TableCell>
                      <TableCell className="font-medium text-green-600">
                        {transaction.profit.toLocaleString()}
                      </TableCell>
                      <TableCell>{transaction.customer}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="kategori">
          <Card>
            <CardHeader>
              <CardTitle>Kategori Bazlı Özet</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kategori</TableHead>
                    <TableHead>İşlem Sayısı</TableHead>
                    <TableHead>Toplam Maliyet (₺)</TableHead>
                    <TableHead>Toplam Satış (₺)</TableHead>
                    <TableHead>Toplam Kar (₺)</TableHead>
                    <TableHead>Kar Marjı (%)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(categoryData).map(([category, data]) => (
                    <TableRow key={category}>
                      <TableCell className="font-medium">{category}</TableCell>
                      <TableCell>{data.count}</TableCell>
                      <TableCell>{data.totalCost.toLocaleString()}</TableCell>
                      <TableCell>{data.totalPrice.toLocaleString()}</TableCell>
                      <TableCell className="font-medium text-green-600">{data.totalProfit.toLocaleString()}</TableCell>
                      <TableCell>{((data.totalProfit / data.totalPrice) * 100).toFixed(2)}%</TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="bg-gray-50 font-bold">
                    <TableCell>TOPLAM</TableCell>
                    <TableCell>{totalTransactions}</TableCell>
                    <TableCell>{totalCost.toLocaleString()}</TableCell>
                    <TableCell>{totalPrice.toLocaleString()}</TableCell>
                    <TableCell className="text-green-600">{totalProfit.toLocaleString()}</TableCell>
                    <TableCell>{profitMargin.toFixed(2)}%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gunluk">
          <Card>
            <CardHeader>
              <CardTitle>Günlük Özet</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tarih</TableHead>
                    <TableHead>İşlem Sayısı</TableHead>
                    <TableHead>Toplam Maliyet (₺)</TableHead>
                    <TableHead>Toplam Satış (₺)</TableHead>
                    <TableHead>Toplam Kar (₺)</TableHead>
                    <TableHead>Kar Marjı (%)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.values(dailyData)
                    .sort((a, b) => b.date.getTime() - a.date.getTime())
                    .map((data, index) => (
                      <TableRow key={index}>
                        <TableCell>{format(data.date, "dd.MM.yyyy")}</TableCell>
                        <TableCell>{data.count}</TableCell>
                        <TableCell>{data.totalCost.toLocaleString()}</TableCell>
                        <TableCell>{data.totalPrice.toLocaleString()}</TableCell>
                        <TableCell className="font-medium text-green-600">
                          {data.totalProfit.toLocaleString()}
                        </TableCell>
                        <TableCell>{((data.totalProfit / data.totalPrice) * 100).toFixed(2)}%</TableCell>
                      </TableRow>
                    ))}
                  <TableRow className="bg-gray-50 font-bold">
                    <TableCell>TOPLAM</TableCell>
                    <TableCell>{totalTransactions}</TableCell>
                    <TableCell>{totalCost.toLocaleString()}</TableCell>
                    <TableCell>{totalPrice.toLocaleString()}</TableCell>
                    <TableCell className="text-green-600">{totalProfit.toLocaleString()}</TableCell>
                    <TableCell>{profitMargin.toFixed(2)}%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grafikler">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Kategori Dağılımı</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6">
                <div className="flex flex-col items-center">
                  <PieChart className="h-48 w-48 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mt-4">Kategori bazlı satış dağılımı</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Günlük Kar Grafiği</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6">
                <div className="flex flex-col items-center">
                  <BarChart className="h-48 w-48 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mt-4">Günlük kar değişimi</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
