"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Edit, Plus, Save, Search, Trash } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface Telefon {
  id: number
  model: string
  marka: string
  resimUrl: string
  alisFiyati: number
  satisFiyati: number
  stok: number
  ozellikler: string
}

export default function TelefonFiyatlari() {
  const [telefonlar, setTelefonlar] = useState<Telefon[]>([
    {
      id: 1,
      model: "iPhone 13",
      marka: "Apple",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 18000,
      satisFiyati: 22000,
      stok: 5,
      ozellikler: "128GB, Siyah",
    },
    {
      id: 2,
      model: "iPhone 13 Pro",
      marka: "Apple",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 25000,
      satisFiyati: 30000,
      stok: 3,
      ozellikler: "256GB, Mavi",
    },
    {
      id: 3,
      model: "Galaxy S22",
      marka: "Samsung",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 15000,
      satisFiyati: 18500,
      stok: 7,
      ozellikler: "128GB, Beyaz",
    },
    {
      id: 4,
      model: "Galaxy S22 Ultra",
      marka: "Samsung",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 22000,
      satisFiyati: 26000,
      stok: 2,
      ozellikler: "256GB, Siyah",
    },
    {
      id: 5,
      model: "Redmi Note 11",
      marka: "Xiaomi",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 6000,
      satisFiyati: 7500,
      stok: 10,
      ozellikler: "128GB, Gri",
    },
    {
      id: 6,
      model: "P50 Pro",
      marka: "Huawei",
      resimUrl: "/placeholder.svg?height=80&width=80",
      alisFiyati: 12000,
      satisFiyati: 15000,
      stok: 4,
      ozellikler: "256GB, Siyah",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<Telefon | null>(null)

  const filteredTelefonlar = telefonlar.filter(
    (telefon) =>
      telefon.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      telefon.marka.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleEdit = (telefon: Telefon) => {
    setEditingId(telefon.id)
    setEditForm({ ...telefon })
  }

  const handleSave = () => {
    if (!editForm) return

    setTelefonlar(telefonlar.map((telefon) => (telefon.id === editingId ? { ...editForm } : telefon)))
    setEditingId(null)
    setEditForm(null)
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditForm(null)
  }

  const handleDelete = (id: number) => {
    setTelefonlar(telefonlar.filter((telefon) => telefon.id !== id))
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/fiyatlar">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Geri
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Telefon Fiyatları</h1>
      </div>

      <div className="flex justify-between mb-6">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Telefon ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" /> Yeni Telefon Ekle
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Görsel</TableHead>
                  <TableHead>Marka</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead>Özellikler</TableHead>
                  <TableHead>Alış Fiyatı (₺)</TableHead>
                  <TableHead>Satış Fiyatı (₺)</TableHead>
                  <TableHead>Stok</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTelefonlar.map((telefon) => (
                  <TableRow key={telefon.id}>
                    <TableCell>
                      <Image
                        src={telefon.resimUrl || "/placeholder.svg"}
                        alt={telefon.model}
                        width={50}
                        height={50}
                        className="rounded-md object-cover"
                      />
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          value={editForm?.marka}
                          onChange={(e) => setEditForm({ ...editForm!, marka: e.target.value })}
                        />
                      ) : (
                        telefon.marka
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          value={editForm?.model}
                          onChange={(e) => setEditForm({ ...editForm!, model: e.target.value })}
                        />
                      ) : (
                        telefon.model
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          value={editForm?.ozellikler}
                          onChange={(e) => setEditForm({ ...editForm!, ozellikler: e.target.value })}
                        />
                      ) : (
                        telefon.ozellikler
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          type="number"
                          value={editForm?.alisFiyati}
                          onChange={(e) => setEditForm({ ...editForm!, alisFiyati: Number(e.target.value) })}
                        />
                      ) : (
                        telefon.alisFiyati.toLocaleString()
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          type="number"
                          value={editForm?.satisFiyati}
                          onChange={(e) => setEditForm({ ...editForm!, satisFiyati: Number(e.target.value) })}
                        />
                      ) : (
                        telefon.satisFiyati.toLocaleString()
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <Input
                          type="number"
                          value={editForm?.stok}
                          onChange={(e) => setEditForm({ ...editForm!, stok: Number(e.target.value) })}
                        />
                      ) : (
                        telefon.stok
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === telefon.id ? (
                        <div className="flex space-x-2">
                          <Button size="sm" onClick={handleSave}>
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancel}>
                            İptal
                          </Button>
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline" onClick={() => handleEdit(telefon)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-500"
                            onClick={() => handleDelete(telefon.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
