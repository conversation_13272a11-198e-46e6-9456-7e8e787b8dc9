"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, Edit, Plus, Save, Search, Trash, ShoppingCart } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useUrunler, type Urun } from "@/lib/db"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

export default function AksesuarlarPage() {
  const router = useRouter()
  const { urun<PERSON>, urun<PERSON>un<PERSON><PERSON>, urun<PERSON>il, urunEkle } = useUrunler()

  const [searchTerm, setSearchTerm] = useState("")
  const [editingId, setEditingId] = useState<number | null>(null)
  const [editForm, setEditForm] = useState<Urun | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>("Tümü")
  const [yeniAksesuar, setYeniAksesuar] = useState<Omit<Urun, "id">>({
    ad: "",
    kategori: "Aksesuar",
    alisFiyati: 0,
    satisFiyati: 0,
    stok: 0,
    resimUrl: "/placeholder.svg?height=80&width=80",
  })

  // Sadece aksesuarları filtrele
  const aksesuarlar = urunler.filter((urun) => urun.kategori === "Aksesuar")

  const filteredAksesuarlar = aksesuarlar.filter(
    (aksesuar) =>
      (selectedCategory === "Tümü" || aksesuar.ad.toLowerCase().includes(selectedCategory.toLowerCase())) &&
      aksesuar.ad.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleEdit = (aksesuar: Urun) => {
    setEditingId(aksesuar.id)
    setEditForm({ ...aksesuar })
  }

  const handleSave = () => {
    if (!editForm) return

    urunGuncelle(editForm.id, editForm)
    setEditingId(null)
    setEditForm(null)
    toast({
      title: "Başarılı",
      description: "Aksesuar bilgileri güncellendi",
    })
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditForm(null)
  }

  const handleDelete = (id: number) => {
    urunSil(id)
    toast({
      title: "Başarılı",
      description: "Aksesuar silindi",
    })
  }

  const handleYeniAksesuarEkle = () => {
    // Validasyon
    if (!yeniAksesuar.ad) {
      toast({
        title: "Hata",
        description: "Lütfen aksesuar adını girin",
        variant: "destructive",
      })
      return
    }

    // Yeni aksesuar ekle
    urunEkle(yeniAksesuar)

    // Formu temizle
    setYeniAksesuar({
      ad: "",
      kategori: "Aksesuar",
      alisFiyati: 0,
      satisFiyati: 0,
      stok: 0,
      resimUrl: "/placeholder.svg?height=80&width=80",
    })

    toast({
      title: "Başarılı",
      description: "Yeni aksesuar başarıyla eklendi",
    })
  }

  const handleSelectAksesuar = (aksesuar: Urun) => {
    // Aksesuar bilgilerini URL parametresi olarak gönder
    router.push(`/?urunId=${aksesuar.id}`)
  }

  // Aksesuar kategorileri
  const categories = ["Tümü", "Kılıf", "Şarj", "Kulaklık", "Ekran Koruyucu"]

  return (
    <div className="container mx-auto p-4">
      <Toaster />
      <div className="flex items-center mb-6">
        <Link href="/urunler">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" /> Geri
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Aksesuarlar</h1>
      </div>

      <div className="flex flex-wrap gap-4 mb-6">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Aksesuar ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              size="sm"
            >
              {category}
            </Button>
          ))}
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button className="ml-auto">
              <Plus className="h-4 w-4 mr-2" /> Yeni Aksesuar Ekle
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Yeni Aksesuar Ekle</DialogTitle>
              <DialogDescription>
                Yeni aksesuar bilgilerini girin. Tüm alanları doldurduğunuzdan emin olun.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="ad" className="text-right">
                  Aksesuar Adı
                </label>
                <Input
                  id="ad"
                  value={yeniAksesuar.ad}
                  onChange={(e) => setYeniAksesuar({ ...yeniAksesuar, ad: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="alisFiyati" className="text-right">
                  Alış Fiyatı
                </label>
                <Input
                  id="alisFiyati"
                  type="number"
                  value={yeniAksesuar.alisFiyati || ""}
                  onChange={(e) => setYeniAksesuar({ ...yeniAksesuar, alisFiyati: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="satisFiyati" className="text-right">
                  Satış Fiyatı
                </label>
                <Input
                  id="satisFiyati"
                  type="number"
                  value={yeniAksesuar.satisFiyati || ""}
                  onChange={(e) => setYeniAksesuar({ ...yeniAksesuar, satisFiyati: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="stok" className="text-right">
                  Stok
                </label>
                <Input
                  id="stok"
                  type="number"
                  value={yeniAksesuar.stok || ""}
                  onChange={(e) => setYeniAksesuar({ ...yeniAksesuar, stok: Number(e.target.value) })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleYeniAksesuarEkle}>
                Ekle
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Görsel</TableHead>
                  <TableHead>Aksesuar Adı</TableHead>
                  <TableHead>Alış Fiyatı (₺)</TableHead>
                  <TableHead>Satış Fiyatı (₺)</TableHead>
                  <TableHead>Stok</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAksesuarlar.length > 0 ? (
                  filteredAksesuarlar.map((aksesuar) => (
                    <TableRow key={aksesuar.id}>
                      <TableCell>
                        <Image
                          src={aksesuar.resimUrl || "/placeholder.svg"}
                          alt={aksesuar.ad}
                          width={50}
                          height={50}
                          className="rounded-md object-cover"
                        />
                      </TableCell>
                      <TableCell>
                        {editingId === aksesuar.id ? (
                          <Input
                            value={editForm?.ad}
                            onChange={(e) => setEditForm({ ...editForm!, ad: e.target.value })}
                          />
                        ) : (
                          aksesuar.ad
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === aksesuar.id ? (
                          <Input
                            type="number"
                            value={editForm?.alisFiyati}
                            onChange={(e) => setEditForm({ ...editForm!, alisFiyati: Number(e.target.value) })}
                          />
                        ) : (
                          aksesuar.alisFiyati.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === aksesuar.id ? (
                          <Input
                            type="number"
                            value={editForm?.satisFiyati}
                            onChange={(e) => setEditForm({ ...editForm!, satisFiyati: Number(e.target.value) })}
                          />
                        ) : (
                          aksesuar.satisFiyati.toLocaleString()
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === aksesuar.id ? (
                          <Input
                            type="number"
                            value={editForm?.stok}
                            onChange={(e) => setEditForm({ ...editForm!, stok: Number(e.target.value) })}
                          />
                        ) : (
                          <div className="flex items-center">
                            {aksesuar.stok}
                            {aksesuar.stok <= 5 && (
                              <Badge variant="destructive" className="ml-2">
                                Az
                              </Badge>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === aksesuar.id ? (
                          <div className="flex space-x-2">
                            <Button size="sm" onClick={handleSave}>
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={handleCancel}>
                              İptal
                            </Button>
                          </div>
                        ) : (
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="default"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleSelectAksesuar(aksesuar)}
                            >
                              <ShoppingCart className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(aksesuar)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-500"
                              onClick={() => handleDelete(aksesuar.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      Aksesuar bulunamadı
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
