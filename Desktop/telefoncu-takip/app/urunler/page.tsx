"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Smartphone, Package } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useUrun<PERSON> } from "@/lib/db"

export default function UrunlerPage() {
  const { urunler } = useUrunler()

  // Telefonları ve aksesuarları filtrele
  const telefonlar = urunler.filter((urun) => urun.kategori === "Telefon").slice(0, 3)
  const aksesuarlar = urunler.filter((urun) => urun.kategori === "Aksesuar").slice(0, 3)

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="sm" className="mr-4">
            ← Ana Sayfa
          </Button>
        </Link>
        <h1 className="text-2xl font-bold"><PERSON><PERSON><PERSON><PERSON></h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Link href="/urunler/telefon">
          <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
            <CardHeader className="flex flex-row items-center gap-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <Smartphone className="h-8 w-8 text-blue-700" />
              </div>
              <div>
                <CardTitle>Telefonlar</CardTitle>
                <CardDescription>Telefon modellerini yönetin</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                {telefonlar.map((telefon) => (
                  <div key={telefon.id} className="flex flex-col items-center">
                    <Image
                      src={telefon.resimUrl || "/placeholder.svg"}
                      alt={telefon.ad}
                      width={80}
                      height={80}
                      className="rounded-md mb-2"
                    />
                    <span className="text-sm font-medium">{telefon.ad}</span>
                    <span className="text-sm text-muted-foreground">₺{telefon.satisFiyati.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/urunler/aksesuar">
          <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
            <CardHeader className="flex flex-row items-center gap-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Package className="h-8 w-8 text-green-700" />
              </div>
              <div>
                <CardTitle>Aksesuarlar</CardTitle>
                <CardDescription>Kılıf, şarj aleti ve diğer aksesuarları yönetin</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                {aksesuarlar.map((aksesuar) => (
                  <div key={aksesuar.id} className="flex flex-col items-center">
                    <Image
                      src={aksesuar.resimUrl || "/placeholder.svg"}
                      alt={aksesuar.ad}
                      width={80}
                      height={80}
                      className="rounded-md mb-2"
                    />
                    <span className="text-sm font-medium">{aksesuar.ad}</span>
                    <span className="text-sm text-muted-foreground">₺{aksesuar.satisFiyati.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
