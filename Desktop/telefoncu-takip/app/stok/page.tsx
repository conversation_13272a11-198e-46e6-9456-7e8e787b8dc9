"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Plus, Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface StokItem {
  id: number
  urunKodu: string
  urunAdi: string
  kategori: string
  marka: string
  model: string
  alisFiyati: number
  satisFiyati: number
  stokMiktari: number
  kritikStokSeviyesi: number
}

export default function StokTakip() {
  const [stokItems, setStokItems] = useState<StokItem[]>([
    {
      id: 1,
      urun<PERSON>od<PERSON>: "TLF-001",
      urun<PERSON><PERSON>: "iPhone 13",
      kategori: "Telefon",
      marka: "Apple",
      model: "iPhone 13",
      alisFiyati: 18000,
      satisFiyati: 22000,
      stokMiktari: 5,
      kritikStokSeviyesi: 2,
    },
    {
      id: 2,
      urunKodu: "TLF-002",
      urunAdi: "Samsung Galaxy S22",
      kategori: "Telefon",
      marka: "Samsung",
      model: "Galaxy S22",
      alisFiyati: 15000,
      satisFiyati: 18500,
      stokMiktari: 3,
      kritikStokSeviyesi: 2,
    },
    {
      id: 3,
      urunKodu: "AKS-001",
      urunAdi: "Silikon Kılıf",
      kategori: "Aksesuar",
      marka: "Generic",
      model: "iPhone 13 Uyumlu",
      alisFiyati: 50,
      satisFiyati: 150,
      stokMiktari: 25,
      kritikStokSeviyesi: 5,
    },
    {
      id: 4,
      urunKodu: "AKS-002",
      urunAdi: "Temperli Cam",
      kategori: "Aksesuar",
      marka: "Generic",
      model: "Universal",
      alisFiyati: 20,
      satisFiyati: 100,
      stokMiktari: 1,
      kritikStokSeviyesi: 10,
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("Tümü")

  const filteredItems = stokItems.filter(
    (item) =>
      (filterCategory === "Tümü" || item.kategori === filterCategory) &&
      (item.urunAdi.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.urunKodu.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.marka.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.model.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const kritikStokItems = stokItems.filter((item) => item.stokMiktari <= item.kritikStokSeviyesi)

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold text-center mb-6">Stok Takip Sistemi</h1>

      {kritikStokItems.length > 0 && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Kritik Stok Uyarısı</AlertTitle>
          <AlertDescription>
            {kritikStokItems.length} ürün kritik stok seviyesinin altında. Lütfen stok durumunu kontrol edin.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Ürün</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stokItems.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Telefon</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stokItems.filter((item) => item.kategori === "Telefon").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Aksesuar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stokItems.filter((item) => item.kategori === "Aksesuar").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Kritik Stok</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{kritikStokItems.length}</div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Ürün ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Kategori" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Tümü">Tümü</SelectItem>
            <SelectItem value="Telefon">Telefon</SelectItem>
            <SelectItem value="Aksesuar">Aksesuar</SelectItem>
            <SelectItem value="Yedek Parça">Yedek Parça</SelectItem>
          </SelectContent>
        </Select>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Yeni Ürün Ekle
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ürün Kodu</TableHead>
                  <TableHead>Ürün Adı</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Marka/Model</TableHead>
                  <TableHead>Alış Fiyatı</TableHead>
                  <TableHead>Satış Fiyatı</TableHead>
                  <TableHead>Stok Miktarı</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.urunKodu}</TableCell>
                    <TableCell>{item.urunAdi}</TableCell>
                    <TableCell>{item.kategori}</TableCell>
                    <TableCell>
                      {item.marka} / {item.model}
                    </TableCell>
                    <TableCell>₺{item.alisFiyati.toLocaleString()}</TableCell>
                    <TableCell>₺{item.satisFiyati.toLocaleString()}</TableCell>
                    <TableCell>{item.stokMiktari}</TableCell>
                    <TableCell>
                      {item.stokMiktari <= 0 ? (
                        <Badge variant="destructive">Stokta Yok</Badge>
                      ) : item.stokMiktari <= item.kritikStokSeviyesi ? (
                        <Badge variant="destructive">Kritik Seviye</Badge>
                      ) : (
                        <Badge variant="outline">Yeterli</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Düzenle
                        </Button>
                        <Button variant="outline" size="sm">
                          Stok Ekle
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
