import { NextRequest, NextResponse } from 'next/server'
import { UrunService } from '@/lib/database'

export async function GET() {
  try {
    const urunler = UrunService.getAll()
    return NextResponse.json(urunler)
  } catch (error) {
    console.error('<PERSON><PERSON><PERSON>nler getirme hatası:', error)
    return NextResponse.json({ error: 'Ürünler getirilemedi' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const yeniUrun = UrunService.create(data)
    return NextResponse.json(yeniUrun)
  } catch (error) {
    console.error('Ürün ekleme hatası:', error)
    return NextResponse.json({ error: 'Ürün eklenemedi' }, { status: 500 })
  }
}
