import { NextRequest, NextResponse } from 'next/server'
import { UrunService } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const urun = UrunService.getById(id)
    
    if (!urun) {
      return NextResponse.json({ error: 'Ürün bulunamadı' }, { status: 404 })
    }
    
    return NextResponse.json(urun)
  } catch (error) {
    console.error('Ürün getirme hatası:', error)
    return NextResponse.json({ error: 'Ürün getirilemedi' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const data = await request.json()
    const guncellenmisUrun = UrunService.update(id, data)
    return NextResponse.json(guncellenmisUrun)
  } catch (error) {
    console.error('<PERSON><PERSON><PERSON><PERSON> güncelleme hatası:', error)
    return NextResponse.json({ error: 'Ürün güncellenemedi' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const basarili = UrunService.delete(id)
    
    if (!basarili) {
      return NextResponse.json({ error: 'Ürün silinemedi' }, { status: 404 })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Ürün silme hatası:', error)
    return NextResponse.json({ error: 'Ürün silinemedi' }, { status: 500 })
  }
}
