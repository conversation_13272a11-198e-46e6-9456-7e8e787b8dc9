import Database from 'better-sqlite3'
import path from 'path'

// Veri tipleri
export interface Urun {
  id: number
  ad: string
  marka?: string
  model?: string
  kategori: string
  alisFiyati: number
  satisFiyati: number
  stok: number
  ozellikler?: string
  resimUrl: string
  createdAt: string
  updatedAt: string
}

export interface Islem {
  id: number
  tarih: string
  kategori: string
  urunId: number
  urunAdi: string
  alisFiyati: number
  satisFiyati: number
  kar: number
  musteriAdi: string
  createdAt: string
  updatedAt: string
}

// Database bağlantısı
let db: Database.Database | null = null

function getDatabase() {
  if (!db) {
    const dbPath = path.join(process.cwd(), 'telefoncu.db')
    db = new Database(dbPath)

    // WAL mode'u etkinleştir (daha iyi performans)
    db.pragma('journal_mode = WAL')

    // Tabloları oluştur
    initializeTables()
  }
  return db
}

function initializeTables() {
  if (!db) return

  // Ürünler tablosu
  db.exec(`
    CREATE TABLE IF NOT EXISTS urunler (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      ad TEXT NOT NULL,
      marka TEXT,
      model TEXT,
      kategori TEXT NOT NULL,
      alisFiyati REAL NOT NULL,
      satisFiyati REAL NOT NULL,
      stok INTEGER NOT NULL,
      ozellikler TEXT,
      resimUrl TEXT NOT NULL,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // İşlemler tablosu
  db.exec(`
    CREATE TABLE IF NOT EXISTS islemler (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tarih TEXT NOT NULL,
      kategori TEXT NOT NULL,
      urunId INTEGER NOT NULL,
      urunAdi TEXT NOT NULL,
      alisFiyati REAL NOT NULL,
      satisFiyati REAL NOT NULL,
      kar REAL NOT NULL,
      musteriAdi TEXT NOT NULL,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (urunId) REFERENCES urunler (id)
    )
  `)

  // Trigger'lar - updatedAt otomatik güncelleme
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_urunler_timestamp
    AFTER UPDATE ON urunler
    BEGIN
      UPDATE urunler SET updatedAt = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `)

  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_islemler_timestamp
    AFTER UPDATE ON islemler
    BEGIN
      UPDATE islemler SET updatedAt = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `)
}

// Ürün servisi
export class UrunService {
  static getAll(): Urun[] {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM urunler ORDER BY id ASC')
    return stmt.all() as Urun[]
  }

  static getById(id: number): Urun | null {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM urunler WHERE id = ?')
    return stmt.get(id) as Urun | null
  }

  static create(data: Omit<Urun, 'id' | 'createdAt' | 'updatedAt'>): Urun {
    const db = getDatabase()
    const stmt = db.prepare(`
      INSERT INTO urunler (ad, marka, model, kategori, alisFiyati, satisFiyati, stok, ozellikler, resimUrl)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      data.ad,
      data.marka || null,
      data.model || null,
      data.kategori,
      data.alisFiyati,
      data.satisFiyati,
      data.stok,
      data.ozellikler || null,
      data.resimUrl
    )

    return this.getById(result.lastInsertRowid as number)!
  }

  static update(id: number, data: Partial<Omit<Urun, 'id' | 'createdAt' | 'updatedAt'>>): Urun {
    const db = getDatabase()

    const fields = []
    const values = []

    if (data.ad !== undefined) { fields.push('ad = ?'); values.push(data.ad) }
    if (data.marka !== undefined) { fields.push('marka = ?'); values.push(data.marka) }
    if (data.model !== undefined) { fields.push('model = ?'); values.push(data.model) }
    if (data.kategori !== undefined) { fields.push('kategori = ?'); values.push(data.kategori) }
    if (data.alisFiyati !== undefined) { fields.push('alisFiyati = ?'); values.push(data.alisFiyati) }
    if (data.satisFiyati !== undefined) { fields.push('satisFiyati = ?'); values.push(data.satisFiyati) }
    if (data.stok !== undefined) { fields.push('stok = ?'); values.push(data.stok) }
    if (data.ozellikler !== undefined) { fields.push('ozellikler = ?'); values.push(data.ozellikler) }
    if (data.resimUrl !== undefined) { fields.push('resimUrl = ?'); values.push(data.resimUrl) }

    if (fields.length === 0) return this.getById(id)!

    values.push(id)

    const stmt = db.prepare(`UPDATE urunler SET ${fields.join(', ')} WHERE id = ?`)
    stmt.run(...values)

    return this.getById(id)!
  }

  static delete(id: number): boolean {
    const db = getDatabase()
    const stmt = db.prepare('DELETE FROM urunler WHERE id = ?')
    const result = stmt.run(id)
    return result.changes > 0
  }

  static updateStock(id: number, miktar: number): boolean {
    const urun = this.getById(id)
    if (!urun) return false

    const yeniStok = urun.stok + miktar
    if (yeniStok < 0) return false

    this.update(id, { stok: yeniStok })
    return true
  }

  static getLowStock(threshold: number = 3): Urun[] {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM urunler WHERE stok <= ? ORDER BY stok ASC')
    return stmt.all(threshold) as Urun[]
  }
}

// İşlem servisi
export class IslemService {
  static getAll(): Islem[] {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM islemler ORDER BY tarih DESC')
    return stmt.all() as Islem[]
  }

  static getById(id: number): Islem | null {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM islemler WHERE id = ?')
    return stmt.get(id) as Islem | null
  }

  static create(data: Omit<Islem, 'id' | 'createdAt' | 'updatedAt'>): Islem {
    const db = getDatabase()
    const stmt = db.prepare(`
      INSERT INTO islemler (tarih, kategori, urunId, urunAdi, alisFiyati, satisFiyati, kar, musteriAdi)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      data.tarih,
      data.kategori,
      data.urunId,
      data.urunAdi,
      data.alisFiyati,
      data.satisFiyati,
      data.kar,
      data.musteriAdi
    )

    return this.getById(result.lastInsertRowid as number)!
  }

  static update(id: number, data: Partial<Omit<Islem, 'id' | 'createdAt' | 'updatedAt'>>): Islem {
    const db = getDatabase()

    const fields = []
    const values = []

    if (data.tarih !== undefined) { fields.push('tarih = ?'); values.push(data.tarih) }
    if (data.kategori !== undefined) { fields.push('kategori = ?'); values.push(data.kategori) }
    if (data.urunId !== undefined) { fields.push('urunId = ?'); values.push(data.urunId) }
    if (data.urunAdi !== undefined) { fields.push('urunAdi = ?'); values.push(data.urunAdi) }
    if (data.alisFiyati !== undefined) { fields.push('alisFiyati = ?'); values.push(data.alisFiyati) }
    if (data.satisFiyati !== undefined) { fields.push('satisFiyati = ?'); values.push(data.satisFiyati) }
    if (data.kar !== undefined) { fields.push('kar = ?'); values.push(data.kar) }
    if (data.musteriAdi !== undefined) { fields.push('musteriAdi = ?'); values.push(data.musteriAdi) }

    if (fields.length === 0) return this.getById(id)!

    values.push(id)

    const stmt = db.prepare(`UPDATE islemler SET ${fields.join(', ')} WHERE id = ?`)
    stmt.run(...values)

    return this.getById(id)!
  }

  static delete(id: number): boolean {
    const db = getDatabase()
    const stmt = db.prepare('DELETE FROM islemler WHERE id = ?')
    const result = stmt.run(id)
    return result.changes > 0
  }

  static getByDateRange(startDate?: string, endDate?: string): Islem[] {
    const db = getDatabase()
    let query = 'SELECT * FROM islemler'
    const params = []

    if (startDate || endDate) {
      query += ' WHERE'
      if (startDate) {
        query += ' tarih >= ?'
        params.push(startDate)
      }
      if (endDate) {
        if (startDate) query += ' AND'
        query += ' tarih <= ?'
        params.push(endDate)
      }
    }

    query += ' ORDER BY tarih DESC'

    const stmt = db.prepare(query)
    return stmt.all(...params) as Islem[]
  }

  static getRecentTransactions(limit: number = 10): Islem[] {
    const db = getDatabase()
    const stmt = db.prepare('SELECT * FROM islemler ORDER BY tarih DESC LIMIT ?')
    return stmt.all(limit) as Islem[]
  }
}

// Veritabanı başlatma ve seed fonksiyonu
export function initializeDatabase() {
  try {
    const db = getDatabase()

    // Eğer hiç ürün yoksa, örnek verileri ekle
    const urunSayisi = db.prepare('SELECT COUNT(*) as count FROM urunler').get() as { count: number }
    if (urunSayisi.count === 0) {
      seedDatabase()
    }

    return true
  } catch (error) {
    console.error('Veritabanı başlatma hatası:', error)
    return false
  }
}

// Örnek verileri ekleme fonksiyonu
function seedDatabase() {
  console.log('Örnek veriler ekleniyor...')

  const ornekUrunler = [
    // Telefonlar
    {
      ad: "iPhone 13",
      marka: "Apple",
      model: "iPhone 13",
      kategori: "Telefon",
      alisFiyati: 18000,
      satisFiyati: 22000,
      stok: 5,
      ozellikler: "128GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "iPhone 13 Pro",
      marka: "Apple",
      model: "iPhone 13 Pro",
      kategori: "Telefon",
      alisFiyati: 25000,
      satisFiyati: 30000,
      stok: 3,
      ozellikler: "256GB, Mavi",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22",
      marka: "Samsung",
      model: "Galaxy S22",
      kategori: "Telefon",
      alisFiyati: 15000,
      satisFiyati: 18500,
      stok: 7,
      ozellikler: "128GB, Beyaz",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22 Ultra",
      marka: "Samsung",
      model: "Galaxy S22 Ultra",
      kategori: "Telefon",
      alisFiyati: 22000,
      satisFiyati: 26000,
      stok: 2,
      ozellikler: "256GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Redmi Note 11",
      marka: "Xiaomi",
      model: "Redmi Note 11",
      kategori: "Telefon",
      alisFiyati: 6000,
      satisFiyati: 7500,
      stok: 10,
      ozellikler: "128GB, Gri",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    // Aksesuarlar
    {
      ad: "iPhone 13 Silikon Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 50,
      satisFiyati: 150,
      stok: 25,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Samsung Galaxy S22 Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 40,
      satisFiyati: 120,
      stok: 30,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Wireless Şarj Aleti",
      kategori: "Aksesuar",
      alisFiyati: 200,
      satisFiyati: 350,
      stok: 15,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "USB-C Kablo",
      kategori: "Aksesuar",
      alisFiyati: 25,
      satisFiyati: 75,
      stok: 50,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Bluetooth Kulaklık",
      kategori: "Aksesuar",
      alisFiyati: 150,
      satisFiyati: 300,
      stok: 20,
      resimUrl: "/placeholder.svg?height=80&width=80",
    }
  ]

  // Ürünleri ekle
  for (const urun of ornekUrunler) {
    UrunService.create(urun)
  }

  // Örnek işlemler ekle
  const ornekIslemler = [
    {
      tarih: "2025-05-17",
      kategori: "Telefon",
      urunId: 1,
      urunAdi: "iPhone 13",
      alisFiyati: 18000,
      satisFiyati: 22000,
      kar: 4000,
      musteriAdi: "Ahmet Yılmaz",
    },
    {
      tarih: "2025-05-16",
      kategori: "Aksesuar",
      urunId: 6,
      urunAdi: "iPhone 13 Silikon Kılıf",
      alisFiyati: 50,
      satisFiyati: 150,
      kar: 100,
      musteriAdi: "Mehmet Demir",
    },
  ]

  for (const islem of ornekIslemler) {
    IslemService.create(islem)
  }

  console.log('Örnek veriler başarıyla eklendi!')
}
