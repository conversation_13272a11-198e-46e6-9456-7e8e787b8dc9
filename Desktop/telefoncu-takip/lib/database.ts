import { PrismaClient } from '@prisma/client'

// Global Prisma client instance
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ['query'],
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Veri tipleri (Prisma'dan türetil<PERSON>)
export type { Urun, Islem } from '@prisma/client'

// Veritabanı işlemleri için servis sınıfları
export class UrunService {
  static async getAll() {
    return await prisma.urun.findMany({
      orderBy: { id: 'asc' }
    })
  }

  static async getById(id: number) {
    return await prisma.urun.findUnique({
      where: { id }
    })
  }

  static async create(data: Omit<Urun, 'id' | 'createdAt' | 'updatedAt'>) {
    return await prisma.urun.create({
      data
    })
  }

  static async update(id: number, data: Partial<Omit<Urun, 'id' | 'createdAt' | 'updatedAt'>>) {
    return await prisma.urun.update({
      where: { id },
      data
    })
  }

  static async delete(id: number) {
    return await prisma.urun.delete({
      where: { id }
    })
  }

  static async updateStock(id: number, miktar: number) {
    const urun = await this.getById(id)
    if (!urun) return false

    const yeniStok = urun.stok + miktar
    if (yeniStok < 0) return false

    await this.update(id, { stok: yeniStok })
    return true
  }

  static async getLowStock(threshold: number = 3) {
    return await prisma.urun.findMany({
      where: {
        stok: {
          lte: threshold
        }
      },
      orderBy: { stok: 'asc' }
    })
  }
}

export class IslemService {
  static async getAll() {
    return await prisma.islem.findMany({
      include: {
        urun: true
      },
      orderBy: { tarih: 'desc' }
    })
  }

  static async getById(id: number) {
    return await prisma.islem.findUnique({
      where: { id },
      include: {
        urun: true
      }
    })
  }

  static async create(data: Omit<Islem, 'id' | 'createdAt' | 'updatedAt'>) {
    return await prisma.islem.create({
      data,
      include: {
        urun: true
      }
    })
  }

  static async update(id: number, data: Partial<Omit<Islem, 'id' | 'createdAt' | 'updatedAt'>>) {
    return await prisma.islem.update({
      where: { id },
      data,
      include: {
        urun: true
      }
    })
  }

  static async delete(id: number) {
    return await prisma.islem.delete({
      where: { id }
    })
  }

  static async getByDateRange(startDate?: Date, endDate?: Date) {
    const where: any = {}
    
    if (startDate || endDate) {
      where.tarih = {}
      if (startDate) where.tarih.gte = startDate
      if (endDate) where.tarih.lte = endDate
    }

    return await prisma.islem.findMany({
      where,
      include: {
        urun: true
      },
      orderBy: { tarih: 'desc' }
    })
  }

  static async getRecentTransactions(limit: number = 10) {
    return await prisma.islem.findMany({
      take: limit,
      include: {
        urun: true
      },
      orderBy: { tarih: 'desc' }
    })
  }
}

// Veritabanı başlatma ve seed fonksiyonu
export async function initializeDatabase() {
  try {
    // Veritabanının çalışıp çalışmadığını kontrol et
    await prisma.$connect()
    
    // Eğer hiç ürün yoksa, örnek verileri ekle
    const urunSayisi = await prisma.urun.count()
    if (urunSayisi === 0) {
      await seedDatabase()
    }
    
    return true
  } catch (error) {
    console.error('Veritabanı başlatma hatası:', error)
    return false
  }
}

// Örnek verileri ekleme fonksiyonu
async function seedDatabase() {
  const ornekUrunler = [
    // Telefonlar
    {
      ad: "iPhone 13",
      marka: "Apple",
      model: "iPhone 13",
      kategori: "Telefon",
      alisFiyati: 18000,
      satisFiyati: 22000,
      stok: 5,
      ozellikler: "128GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "iPhone 13 Pro",
      marka: "Apple",
      model: "iPhone 13 Pro",
      kategori: "Telefon",
      alisFiyati: 25000,
      satisFiyati: 30000,
      stok: 3,
      ozellikler: "256GB, Mavi",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22",
      marka: "Samsung",
      model: "Galaxy S22",
      kategori: "Telefon",
      alisFiyati: 15000,
      satisFiyati: 18500,
      stok: 7,
      ozellikler: "128GB, Beyaz",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22 Ultra",
      marka: "Samsung",
      model: "Galaxy S22 Ultra",
      kategori: "Telefon",
      alisFiyati: 22000,
      satisFiyati: 26000,
      stok: 2,
      ozellikler: "256GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Redmi Note 11",
      marka: "Xiaomi",
      model: "Redmi Note 11",
      kategori: "Telefon",
      alisFiyati: 6000,
      satisFiyati: 7500,
      stok: 10,
      ozellikler: "128GB, Gri",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    // Aksesuarlar
    {
      ad: "iPhone 13 Silikon Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 50,
      satisFiyati: 150,
      stok: 25,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Samsung Galaxy S22 Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 40,
      satisFiyati: 120,
      stok: 30,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Wireless Şarj Aleti",
      kategori: "Aksesuar",
      alisFiyati: 200,
      satisFiyati: 350,
      stok: 15,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "USB-C Kablo",
      kategori: "Aksesuar",
      alisFiyati: 25,
      satisFiyati: 75,
      stok: 50,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Bluetooth Kulaklık",
      kategori: "Aksesuar",
      alisFiyati: 150,
      satisFiyati: 300,
      stok: 20,
      resimUrl: "/placeholder.svg?height=80&width=80",
    }
  ]

  // Ürünleri ekle
  for (const urun of ornekUrunler) {
    await prisma.urun.create({ data: urun })
  }

  // Örnek işlemler ekle
  const ornekIslemler = [
    {
      tarih: new Date("2025-05-17"),
      kategori: "Telefon",
      urunId: 1,
      urunAdi: "iPhone 13",
      alisFiyati: 18000,
      satisFiyati: 22000,
      kar: 4000,
      musteriAdi: "Ahmet Yılmaz",
    },
    {
      tarih: new Date("2025-05-16"),
      kategori: "Aksesuar",
      urunId: 6,
      urunAdi: "iPhone 13 Silikon Kılıf",
      alisFiyati: 50,
      satisFiyati: 150,
      kar: 100,
      musteriAdi: "Mehmet Demir",
    },
  ]

  for (const islem of ornekIslemler) {
    await prisma.islem.create({ data: islem })
  }

  console.log('Örnek veriler başarıyla eklendi!')
}
