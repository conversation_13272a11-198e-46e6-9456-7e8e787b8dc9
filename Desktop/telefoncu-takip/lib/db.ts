"use client"

// Veritabanı bağlantısı ve hooks
import { useState, useEffect } from "react"
import { UrunService, IslemService, initializeDatabase } from "./database"
import type { Urun, Islem } from "./database"

// Veri tiplerini export et
export type { Urun, Islem }

// LocalStorage anahtarları (migration için)
const URUNLER_KEY = "telefoncu_urunler"
const ISLEMLER_KEY = "telefoncu_islemler"

// LocalStorage'dan veritabanına veri aktarma fonksiyonu
async function migrateFromLocalStorage() {
  try {
    // Önce mevcut veritabanında veri var mı kontrol et
    const mevcutUrunler = await UrunService.getAll()
    if (mevcutUrunler.length > 0) {
      return // Zaten veri var, migration yapma
    }

    // LocalStorage'dan verileri al
    const storedUrunler = localStorage.getItem(URUNLER_KEY)
    const storedIslemler = localStorage.getItem(ISLEMLER_KEY)

    if (storedUrunler) {
      const urunler = JSON.parse(storedUrunler)
      for (const urun of urunler) {
        const { id, ...urunData } = urun // id'yi çıkar, veritabanı otomatik oluşturacak
        await UrunService.create(urunData)
      }
      console.log('Ürünler localStorage\'dan veritabanına aktarıldı')
    }

    if (storedIslemler) {
      const islemler = JSON.parse(storedIslemler)
      for (const islem of islemler) {
        const { id, tarih, ...islemData } = islem
        await IslemService.create({
          ...islemData,
          tarih: new Date(tarih) // String'den Date'e çevir
        })
      }
      console.log('İşlemler localStorage\'dan veritabanına aktarıldı')
    }

    // Migration tamamlandıktan sonra localStorage'ı temizle (isteğe bağlı)
    // localStorage.removeItem(URUNLER_KEY)
    // localStorage.removeItem(ISLEMLER_KEY)

  } catch (error) {
    console.error('Migration hatası:', error)
  }
}

// Veritabanı işlemleri - Ürünler
export function useUrunler() {
  const [urunler, setUrunler] = useState<Urun[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // Ürünleri veritabanından yükle
  useEffect(() => {
    async function loadData() {
      try {
        // Veritabanını başlat
        await initializeDatabase()

        // LocalStorage'dan migration yap
        await migrateFromLocalStorage()

        // Ürünleri yükle
        const data = await UrunService.getAll()
        setUrunler(data)
      } catch (error) {
        console.error('Ürünler yüklenirken hata:', error)
      } finally {
        setYukleniyor(false)
      }
    }

    loadData()
  }, [])

  // Ürün ekle
  const urunEkle = async (urun: Omit<Urun, "id" | "createdAt" | "updatedAt">) => {
    try {
      const yeniUrun = await UrunService.create(urun)
      setUrunler(prev => [...prev, yeniUrun])
      return yeniUrun
    } catch (error) {
      console.error('Ürün eklenirken hata:', error)
      throw error
    }
  }

  // Ürün güncelle
  const urunGuncelle = async (id: number, guncelUrun: Partial<Omit<Urun, "id" | "createdAt" | "updatedAt">>) => {
    try {
      const guncellenmisUrun = await UrunService.update(id, guncelUrun)
      setUrunler(prev => prev.map(urun => urun.id === id ? guncellenmisUrun : urun))
      return guncellenmisUrun
    } catch (error) {
      console.error('Ürün güncellenirken hata:', error)
      throw error
    }
  }

  // Ürün sil
  const urunSil = async (id: number) => {
    try {
      await UrunService.delete(id)
      setUrunler(prev => prev.filter(urun => urun.id !== id))
      return true
    } catch (error) {
      console.error('Ürün silinirken hata:', error)
      throw error
    }
  }

  // Stok güncelle
  const stokGuncelle = async (id: number, miktar: number) => {
    try {
      const basarili = await UrunService.updateStock(id, miktar)
      if (basarili) {
        // Local state'i güncelle
        const guncelUrun = await UrunService.getById(id)
        if (guncelUrun) {
          setUrunler(prev => prev.map(urun => urun.id === id ? guncelUrun : urun))
        }
      }
      return basarili
    } catch (error) {
      console.error('Stok güncellenirken hata:', error)
      return false
    }
  }

  return { urunler, yukleniyor, urunEkle, urunGuncelle, urunSil, stokGuncelle }
}

// Veritabanı işlemleri - İşlemler
export function useIslemler() {
  const [islemler, setIslemler] = useState<Islem[]>([])
  const [yukleniyor, setYukleniyor] = useState(true)

  // İşlemleri veritabanından yükle
  useEffect(() => {
    async function loadData() {
      try {
        // Veritabanını başlat
        await initializeDatabase()

        // LocalStorage'dan migration yap
        await migrateFromLocalStorage()

        // İşlemleri yükle
        const data = await IslemService.getAll()
        setIslemler(data)
      } catch (error) {
        console.error('İşlemler yüklenirken hata:', error)
      } finally {
        setYukleniyor(false)
      }
    }

    loadData()
  }, [])

  // İşlem ekle
  const islemEkle = async (islem: Omit<Islem, "id" | "createdAt" | "updatedAt">) => {
    try {
      const yeniIslem = await IslemService.create(islem)
      setIslemler(prev => [yeniIslem, ...prev]) // En yeniler üstte
      return yeniIslem
    } catch (error) {
      console.error('İşlem eklenirken hata:', error)
      throw error
    }
  }

  // İşlem sil
  const islemSil = async (id: number) => {
    try {
      // Silinecek işlemi bul
      const silinecekIslem = islemler.find((islem) => islem.id === id)

      if (silinecekIslem) {
        // Eğer işlem bir ürüne bağlıysa, stok miktarını geri al
        if (silinecekIslem.urunId > 0) {
          await UrunService.updateStock(silinecekIslem.urunId, 1)
        }

        // İşlemi sil
        await IslemService.delete(id)
        setIslemler(prev => prev.filter(islem => islem.id !== id))
        return true
      }

      return false
    } catch (error) {
      console.error('İşlem silinirken hata:', error)
      throw error
    }
  }

  // İşlem güncelle
  const islemGuncelle = async (id: number, guncelIslem: Partial<Omit<Islem, "id" | "createdAt" | "updatedAt">>) => {
    try {
      // Güncellenecek işlemi bul
      const eskiIslem = islemler.find((islem) => islem.id === id)

      if (eskiIslem) {
        // Eğer ürün ID'si değiştiyse stok güncelle
        if (guncelIslem.urunId !== undefined && eskiIslem.urunId !== guncelIslem.urunId) {
          // Eski ürünün stokunu geri al
          if (eskiIslem.urunId > 0) {
            await UrunService.updateStock(eskiIslem.urunId, 1)
          }

          // Yeni ürünün stokunu düş
          if (guncelIslem.urunId > 0) {
            await UrunService.updateStock(guncelIslem.urunId, -1)
          }
        }

        // Kar hesapla (eğer alış veya satış fiyatı değiştiyse)
        let yeniKar = eskiIslem.kar
        if (guncelIslem.alisFiyati !== undefined || guncelIslem.satisFiyati !== undefined) {
          const alisFiyati = guncelIslem.alisFiyati !== undefined ? guncelIslem.alisFiyati : eskiIslem.alisFiyati
          const satisFiyati = guncelIslem.satisFiyati !== undefined ? guncelIslem.satisFiyati : eskiIslem.satisFiyati
          yeniKar = satisFiyati - alisFiyati
        }

        // İşlemi güncelle
        const guncellenmisIslem = await IslemService.update(id, {
          ...guncelIslem,
          kar: guncelIslem.kar !== undefined ? guncelIslem.kar : yeniKar
        })

        setIslemler(prev => prev.map(islem => islem.id === id ? guncellenmisIslem : islem))
        return true
      }

      return false
    } catch (error) {
      console.error('İşlem güncellenirken hata:', error)
      throw error
    }
  }

  return { islemler, yukleniyor, islemEkle, islemSil, islemGuncelle }
}

// Rapor oluşturma
export function raporOlustur(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  // Tarih filtreleme
  let filtrelenmisIslemler = [...islemler]
  if (baslangicTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih >= baslangicTarihi)
  }
  if (bitisTarihi) {
    filtrelenmisIslemler = filtrelenmisIslemler.filter((islem) => islem.tarih <= bitisTarihi)
  }

  // Toplam değerler
  const toplamIslem = filtrelenmisIslemler.length
  const toplamMaliyet = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.alisFiyati, 0)
  const toplamSatis = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.satisFiyati, 0)
  const toplamKar = filtrelenmisIslemler.reduce((sum, islem) => sum + islem.kar, 0)
  const karMarji = toplamSatis > 0 ? (toplamKar / toplamSatis) * 100 : 0

  // Kategori bazlı özet
  const kategoriBazli = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      if (!acc[islem.kategori]) {
        acc[islem.kategori] = {
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[islem.kategori].islemSayisi += 1
      acc[islem.kategori].toplamMaliyet += islem.alisFiyati
      acc[islem.kategori].toplamSatis += islem.satisFiyati
      acc[islem.kategori].toplamKar += islem.kar

      return acc
    },
    {} as Record<string, { islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }>,
  )

  // Günlük özet
  const gunlukOzet = filtrelenmisIslemler.reduce(
    (acc, islem) => {
      const tarihStr = islem.tarih.toISOString().split("T")[0]

      if (!acc[tarihStr]) {
        acc[tarihStr] = {
          tarih: islem.tarih,
          islemSayisi: 0,
          toplamMaliyet: 0,
          toplamSatis: 0,
          toplamKar: 0,
        }
      }

      acc[tarihStr].islemSayisi += 1
      acc[tarihStr].toplamMaliyet += islem.alisFiyati
      acc[tarihStr].toplamSatis += islem.satisFiyati
      acc[tarihStr].toplamKar += islem.kar

      return acc
    },
    {} as Record<
      string,
      { tarih: Date; islemSayisi: number; toplamMaliyet: number; toplamSatis: number; toplamKar: number }
    >,
  )

  return {
    toplamIslem,
    toplamMaliyet,
    toplamSatis,
    toplamKar,
    karMarji,
    kategoriBazli,
    gunlukOzet,
    islemler: filtrelenmisIslemler,
  }
}

// CSV formatında rapor indirme
export function raporuIndir(islemler: Islem[], baslangicTarihi?: Date, bitisTarihi?: Date) {
  const rapor = raporOlustur(islemler, baslangicTarihi, bitisTarihi)

  // CSV başlıkları
  let csv = "ID,Tarih,Kategori,Ürün Adı,Alış Fiyatı,Satış Fiyatı,Kar,Müşteri Adı\n"

  // İşlemleri CSV'ye ekle
  rapor.islemler.forEach((islem) => {
    csv += `${islem.id},${islem.tarih.toLocaleDateString()},${islem.kategori},${islem.urunAdi},${islem.alisFiyati},${islem.satisFiyati},${islem.kar},${islem.musteriAdi}\n`
  })

  // Özet bilgileri ekle
  csv += "\nÖzet Bilgiler\n"
  csv += `Toplam İşlem,${rapor.toplamIslem}\n`
  csv += `Toplam Maliyet,${rapor.toplamMaliyet}\n`
  csv += `Toplam Satış,${rapor.toplamSatis}\n`
  csv += `Toplam Kar,${rapor.toplamKar}\n`
  csv += `Kar Marjı,%${rapor.karMarji.toFixed(2)}\n`

  // Kategori bazlı özet
  csv += "\nKategori Bazlı Özet\n"
  csv += "Kategori,İşlem Sayısı,Toplam Maliyet,Toplam Satış,Toplam Kar,Kar Marjı\n"
  Object.entries(rapor.kategoriBazli).forEach(([kategori, data]) => {
    const karMarji = data.toplamSatis > 0 ? (data.toplamKar / data.toplamSatis) * 100 : 0
    csv += `${kategori},${data.islemSayisi},${data.toplamMaliyet},${data.toplamSatis},${data.toplamKar},%${karMarji.toFixed(2)}\n`
  })

  // CSV dosyasını indir
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.setAttribute("href", url)
  link.setAttribute("download", `telefoncu_rapor_${new Date().toISOString().split("T")[0]}.csv`)
  link.style.visibility = "hidden"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
