// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Urun {
  id          Int      @id @default(autoincrement())
  ad          String
  marka       String?
  model       String?
  kategori    String
  alisFiyati  Float
  satisFiyati Float
  stok        Int
  ozellikler  String?
  resimUrl    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // İlişkiler
  islemler    Islem[]

  @@map("urunler")
}

model Islem {
  id          Int      @id @default(autoincrement())
  tarih       DateTime
  kategori    String
  urunId      Int
  urunAdi     String
  alisFiyati  Float
  satisFiyati Float
  kar         Float
  musteriAdi  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // İlişkiler
  urun        Urun     @relation(fields: [urunId], references: [id])

  @@map("islemler")
}
