import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('Veritabanı seed işlemi başlıyor...')

  // Önce mevcut verileri kontrol et
  const mevcutUrunler = await prisma.urun.count()
  if (mevcutUrunler > 0) {
    console.log('Veritabanında zaten veri var, seed işlemi atlanıyor.')
    return
  }

  // Örnek ürünler
  const ornekUrunler = [
    // Telefonlar
    {
      ad: "iPhone 13",
      marka: "Apple",
      model: "iPhone 13",
      kategori: "Telefon",
      alisFiyati: 18000,
      satisFiyati: 22000,
      stok: 5,
      ozellikler: "128GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "iPhone 13 Pro",
      marka: "Apple",
      model: "iPhone 13 Pro",
      kategori: "Telefon",
      alisFiyati: 25000,
      satisFiyati: 30000,
      stok: 3,
      ozellikler: "256GB, Mavi",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22",
      marka: "Samsung",
      model: "Galaxy S22",
      kategori: "Telefon",
      alisFiyati: 15000,
      satisFiyati: 18500,
      stok: 7,
      ozellikler: "128GB, Beyaz",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Galaxy S22 Ultra",
      marka: "Samsung",
      model: "Galaxy S22 Ultra",
      kategori: "Telefon",
      alisFiyati: 22000,
      satisFiyati: 26000,
      stok: 2,
      ozellikler: "256GB, Siyah",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Redmi Note 11",
      marka: "Xiaomi",
      model: "Redmi Note 11",
      kategori: "Telefon",
      alisFiyati: 6000,
      satisFiyati: 7500,
      stok: 10,
      ozellikler: "128GB, Gri",
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    // Aksesuarlar
    {
      ad: "iPhone 13 Silikon Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 50,
      satisFiyati: 150,
      stok: 25,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Samsung Galaxy S22 Kılıf",
      kategori: "Aksesuar",
      alisFiyati: 40,
      satisFiyati: 120,
      stok: 30,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Wireless Şarj Aleti",
      kategori: "Aksesuar",
      alisFiyati: 200,
      satisFiyati: 350,
      stok: 15,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "USB-C Kablo",
      kategori: "Aksesuar",
      alisFiyati: 25,
      satisFiyati: 75,
      stok: 50,
      resimUrl: "/placeholder.svg?height=80&width=80",
    },
    {
      ad: "Bluetooth Kulaklık",
      kategori: "Aksesuar",
      alisFiyati: 150,
      satisFiyati: 300,
      stok: 20,
      resimUrl: "/placeholder.svg?height=80&width=80",
    }
  ]

  // Ürünleri ekle
  console.log('Ürünler ekleniyor...')
  for (const urun of ornekUrunler) {
    await prisma.urun.create({ data: urun })
  }

  // Örnek işlemler ekle
  console.log('Örnek işlemler ekleniyor...')
  const ornekIslemler = [
    {
      tarih: new Date("2025-05-17"),
      kategori: "Telefon",
      urunId: 1,
      urunAdi: "iPhone 13",
      alisFiyati: 18000,
      satisFiyati: 22000,
      kar: 4000,
      musteriAdi: "Ahmet Yılmaz",
    },
    {
      tarih: new Date("2025-05-16"),
      kategori: "Aksesuar",
      urunId: 6,
      urunAdi: "iPhone 13 Silikon Kılıf",
      alisFiyati: 50,
      satisFiyati: 150,
      kar: 100,
      musteriAdi: "Mehmet Demir",
    },
  ]

  for (const islem of ornekIslemler) {
    await prisma.islem.create({ data: islem })
  }

  console.log('Seed işlemi tamamlandı!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
